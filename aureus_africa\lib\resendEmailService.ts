/**
 * RESEND EMAIL SERVICE
 * 
 * Comprehensive email service using Resend API for:
 * - Email verification codes
 * - Account security notifications
 * - Newsletter and bulk email functionality
 * - Password reset emails
 * - Account change confirmations
 */

import { Resend } from 'resend';
import { supabase } from './supabase';
import { emailPreferencesService } from './emailPreferencesService';

// Environment configuration
const RESEND_API_KEY = import.meta.env.VITE_RESEND_API_KEY || process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = import.meta.env.VITE_RESEND_FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = import.meta.env.VITE_RESEND_FROM_NAME || process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Initialize Resend client
let resend: Resend | null = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Resend email service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize Resend service:', error);
}

export interface EmailVerificationData {
  email: string;
  code: string;
  purpose: 'registration' | 'account_update' | 'withdrawal' | 'password_reset' | 'telegram_connection';
  userName?: string;
  expiryMinutes?: number;
}

export interface BulkEmailData {
  recipients: string[];
  subject: string;
  htmlContent: string;
  textContent?: string;
  templateData?: Record<string, any>;
}

export interface EmailDeliveryResult {
  success: boolean;
  messageId?: string;
  error?: string;
  deliveryStatus?: 'sent' | 'delivered' | 'bounced' | 'failed';
}

export interface NewsletterSubscription {
  email: string;
  categories: string[];
  preferences: Record<string, any>;
  unsubscribeToken: string;
}

class ResendEmailService {
  private isConfigured(): boolean {
    return resend !== null && !!RESEND_API_KEY;
  }

  /**
   * Send email verification code
   */
  async sendVerificationCode(data: EmailVerificationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured()) {
      console.error('❌ Resend service not configured');
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const { email, code, purpose, userName, expiryMinutes = 15 } = data;

      // Get user ID for preference checking
      const { data: userData } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      const userId = userData?.id;

      // Generate email content based on purpose
      const emailContent = this.generateVerificationEmailContent(code, purpose, userName, expiryMinutes);

      const result = await resend!.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'verification' },
          { name: 'purpose', value: purpose }
        ]
      });

      if (result.error) {
        console.error('❌ Resend API error:', result.error);
        return { success: false, error: result.error.message };
      }

      // Log email delivery
      await this.logEmailDelivery({
        email,
        emailType: `verification_${purpose}`,
        resendMessageId: result.data?.id,
        status: 'sent',
        subject: emailContent.subject,
        userId
      });

      console.log(`✅ Verification email sent to ${email} (${purpose})`);
      return { 
        success: true, 
        messageId: result.data?.id,
        deliveryStatus: 'sent'
      };

    } catch (error) {
      console.error('❌ Error sending verification email:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email: string, resetToken: string, userName?: string): Promise<EmailDeliveryResult> {
    if (!this.isConfigured()) {
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const resetLink = `${window.location.origin}/reset-password?token=${resetToken}`;
      const emailContent = this.generatePasswordResetEmailContent(resetLink, userName);

      const result = await resend!.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'password_reset' }
        ]
      });

      if (result.error) {
        return { success: false, error: result.error.message };
      }

      await this.logEmailDelivery({
        email,
        emailType: 'password_reset',
        resendMessageId: result.data?.id,
        status: 'sent',
        subject: emailContent.subject
      });

      return { success: true, messageId: result.data?.id };

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send account change notification
   */
  async sendAccountChangeNotification(
    email: string, 
    changes: Record<string, any>, 
    userName?: string
  ): Promise<EmailDeliveryResult> {
    if (!this.isConfigured()) {
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const emailContent = this.generateAccountChangeEmailContent(changes, userName);

      const result = await resend!.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'account_change' }
        ]
      });

      if (result.error) {
        return { success: false, error: result.error.message };
      }

      await this.logEmailDelivery({
        email,
        emailType: 'account_change',
        resendMessageId: result.data?.id,
        status: 'sent',
        subject: emailContent.subject
      });

      return { success: true, messageId: result.data?.id };

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send bulk email (newsletter/announcements)
   */
  async sendBulkEmail(data: BulkEmailData): Promise<EmailDeliveryResult[]> {
    if (!this.isConfigured()) {
      return [{ success: false, error: 'Email service not configured' }];
    }

    const results: EmailDeliveryResult[] = [];
    const { recipients, subject, htmlContent, textContent } = data;

    // Send in batches to avoid rate limits
    const batchSize = 50;
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      try {
        const result = await resend!.emails.send({
          from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
          to: batch,
          subject,
          html: htmlContent,
          text: textContent || this.stripHtml(htmlContent),
          tags: [
            { name: 'category', value: 'bulk_email' },
            { name: 'batch', value: `${Math.floor(i / batchSize) + 1}` }
          ]
        });

        if (result.error) {
          results.push({ success: false, error: result.error.message });
        } else {
          results.push({ success: true, messageId: result.data?.id });

          // Log bulk email delivery
          for (const email of batch) {
            await this.logEmailDelivery({
              email,
              emailType: 'bulk_email',
              resendMessageId: result.data?.id,
              status: 'sent',
              subject
            });
          }
        }

        // Rate limiting delay
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        results.push({ 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return results;
  }

  /**
   * Generate verification email content
   */
  private generateVerificationEmailContent(
    code: string, 
    purpose: string, 
    userName?: string,
    expiryMinutes: number = 15
  ) {
    const greeting = userName ? `Hello ${userName}` : 'Hello';
    const purposeText = {
      registration: 'complete your account registration',
      account_update: 'confirm your account changes',
      withdrawal: 'authorize your withdrawal request',
      password_reset: 'reset your password',
      telegram_connection: 'connect your Telegram account'
    }[purpose] || 'verify your email';

    const subject = `Your Aureus Alliance verification code: ${code}`;

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Email Verification</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
            </div>
            
            <h2>Email Verification Required</h2>
            
            <p>${greeting},</p>
            
            <p>You need to verify your email address to ${purposeText}.</p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
              <h3 style="margin: 0; color: #D4AF37;">Your Verification Code</h3>
              <div style="font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 15px 0; color: #333;">
                ${code}
              </div>
              <p style="margin: 0; color: #666; font-size: 14px;">
                This code expires in ${expiryMinutes} minutes
              </p>
            </div>
            
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>Never share this code with anyone</li>
              <li>Aureus Alliance will never ask for this code via phone or email</li>
              <li>If you didn't request this verification, please ignore this email</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This email was sent by Aureus Alliance Holdings. If you have questions, 
              please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Email Verification
      
      ${greeting},
      
      You need to verify your email address to ${purposeText}.
      
      Your verification code: ${code}
      
      This code expires in ${expiryMinutes} minutes.
      
      Security Notice:
      - Never share this code with anyone
      - Aureus Alliance will never ask for this code via phone or email
      - If you didn't request this verification, please ignore this email
      
      This email was sent by Aureus Alliance Holdings.
    `;

    return { subject, html, text };
  }

  /**
   * Generate password reset email content
   */
  private generatePasswordResetEmailContent(resetLink: string, userName?: string) {
    const greeting = userName ? `Hello ${userName}` : 'Hello';
    
    const subject = 'Reset your Aureus Alliance password';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Password Reset</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
            </div>
            
            <h2>Password Reset Request</h2>
            
            <p>${greeting},</p>
            
            <p>We received a request to reset your password. Click the button below to create a new password:</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" 
                 style="background: #D4AF37; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                Reset Password
              </a>
            </div>
            
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">${resetLink}</p>
            
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>This link expires in 24 hours</li>
              <li>If you didn't request this reset, please ignore this email</li>
              <li>Your password will not change until you create a new one</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This email was sent by Aureus Alliance Holdings. If you have questions, 
              please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Password Reset
      
      ${greeting},
      
      We received a request to reset your password. 
      
      Reset your password by visiting this link: ${resetLink}
      
      Security Notice:
      - This link expires in 24 hours
      - If you didn't request this reset, please ignore this email
      - Your password will not change until you create a new one
      
      This email was sent by Aureus Alliance Holdings.
    `;

    return { subject, html, text };
  }

  /**
   * Generate account change notification content
   */
  private generateAccountChangeEmailContent(changes: Record<string, any>, userName?: string) {
    const greeting = userName ? `Hello ${userName}` : 'Hello';
    const changesList = Object.entries(changes)
      .map(([field, value]) => `<li><strong>${field}:</strong> ${value}</li>`)
      .join('');

    const subject = 'Your Aureus Alliance account has been updated';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Account Update Notification</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
            </div>
            
            <h2>Account Update Notification</h2>
            
            <p>${greeting},</p>
            
            <p>Your account information has been successfully updated. Here are the changes:</p>
            
            <ul style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
              ${changesList}
            </ul>
            
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>If you didn't make these changes, please contact support immediately</li>
              <li>Review your account regularly for unauthorized changes</li>
              <li>Keep your login credentials secure</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This email was sent by Aureus Alliance Holdings. If you have questions, 
              please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Account Update
      
      ${greeting},
      
      Your account information has been successfully updated.
      
      Changes made:
      ${Object.entries(changes).map(([field, value]) => `- ${field}: ${value}`).join('\n')}
      
      Security Notice:
      - If you didn't make these changes, please contact support immediately
      - Review your account regularly for unauthorized changes
      - Keep your login credentials secure
      
      This email was sent by Aureus Alliance Holdings.
    `;

    return { subject, html, text };
  }

  /**
   * Log email delivery to database (using existing email_delivery_log table)
   */
  private async logEmailDelivery(data: {
    email: string;
    emailType: string;
    resendMessageId?: string;
    status: string;
    userId?: number;
    subject?: string;
  }) {
    try {
      await supabase
        .from('email_delivery_log')
        .insert({
          user_id: data.userId,
          email_address: data.email, // Note: existing table uses 'email_address'
          email_type: data.emailType,
          resend_message_id: data.resendMessageId,
          delivery_status: data.status, // Note: existing table uses 'delivery_status'
          subject: data.subject,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging email delivery:', error);
    }
  }

  /**
   * Strip HTML tags for text content
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

// Export singleton instance
export const resendEmailService = new ResendEmailService();
export default resendEmailService;
