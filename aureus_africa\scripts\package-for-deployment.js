#!/usr/bin/env node

/**
 * Deployment Packaging Script
 * Creates deployment-ready packages for different hosting environments
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');
const DEPLOYMENT_DIR = path.join(__dirname, '..', 'deployment-packages');

async function createDeploymentDirectory() {
  if (fs.existsSync(DEPLOYMENT_DIR)) {
    await execAsync(`rimraf "${DEPLOYMENT_DIR}"`);
  }
  fs.mkdirSync(DEPLOYMENT_DIR, { recursive: true });
  console.log('✅ Created deployment packages directory');
}

function copyDistFiles(targetDir) {
  if (!fs.existsSync(DIST_DIR)) {
    throw new Error('dist directory not found. Run "npm run build" first.');
  }

  // Copy all files from dist to target directory
  const copyRecursive = (src, dest) => {
    if (fs.statSync(src).isDirectory()) {
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
      }
      fs.readdirSync(src).forEach(file => {
        copyRecursive(path.join(src, file), path.join(dest, file));
      });
    } else {
      fs.copyFileSync(src, dest);
    }
  };

  copyRecursive(DIST_DIR, targetDir);
}

function createCPanelPackage() {
  console.log('📦 Creating cPanel deployment package...');
  
  const cpanelDir = path.join(DEPLOYMENT_DIR, 'cpanel-public_html');
  fs.mkdirSync(cpanelDir, { recursive: true });
  
  // Copy dist files
  copyDistFiles(cpanelDir);
  
  // Create .htaccess for client-side routing
  const htaccessContent = `# Aureus Africa - Client-side routing support
RewriteEngine On

# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>`;

  fs.writeFileSync(path.join(cpanelDir, '.htaccess'), htaccessContent);
  
  // Create deployment instructions
  const instructionsContent = `# cPanel Deployment Instructions

## Upload Process
1. Upload all files from this directory to your cPanel public_html folder
2. Ensure the .htaccess file is uploaded (it may be hidden)
3. Set proper file permissions if needed (644 for files, 755 for directories)

## Verification
- Visit your domain to verify the application loads
- Check that all routes work (refresh on any page should not show 404)
- Verify that assets (CSS, JS, images) load correctly

## Troubleshooting
- If routes don't work: Ensure .htaccess is uploaded and mod_rewrite is enabled
- If assets don't load: Check file paths and permissions
- If blank page: Check browser console for JavaScript errors

## Files Included
- index.html (main application file)
- assets/ (JavaScript, CSS, and other assets)
- manifest.json (PWA manifest)
- .htaccess (Apache configuration for client-side routing)
`;

  fs.writeFileSync(path.join(cpanelDir, 'DEPLOYMENT_INSTRUCTIONS.md'), instructionsContent);
  
  console.log('✅ cPanel package created');
  return cpanelDir;
}

function createNetlifyPackage() {
  console.log('📦 Creating Netlify deployment package...');
  
  const netlifyDir = path.join(DEPLOYMENT_DIR, 'netlify');
  fs.mkdirSync(netlifyDir, { recursive: true });
  
  // Copy dist files
  copyDistFiles(netlifyDir);
  
  // Create _redirects file for client-side routing
  const redirectsContent = `# Netlify redirects for client-side routing
/*    /index.html   200

# API redirects (if using Netlify Functions)
/api/*  /.netlify/functions/:splat  200
`;

  fs.writeFileSync(path.join(netlifyDir, '_redirects'), redirectsContent);
  
  // Create netlify.toml configuration
  const netlifyTomlContent = `[build]
  publish = "."
  
[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
`;

  fs.writeFileSync(path.join(netlifyDir, 'netlify.toml'), netlifyTomlContent);
  
  console.log('✅ Netlify package created');
  return netlifyDir;
}

function createVercelPackage() {
  console.log('📦 Creating Vercel deployment package...');
  
  const vercelDir = path.join(DEPLOYMENT_DIR, 'vercel');
  fs.mkdirSync(vercelDir, { recursive: true });
  
  // Copy dist files
  copyDistFiles(vercelDir);
  
  // Create vercel.json configuration
  const vercelJsonContent = {
    "version": 2,
    "builds": [
      {
        "src": "package.json",
        "use": "@vercel/static-build",
        "config": {
          "distDir": "."
        }
      }
    ],
    "routes": [
      {
        "src": "/assets/(.*)",
        "headers": {
          "cache-control": "public, max-age=********, immutable"
        }
      },
      {
        "src": "/(.*)",
        "dest": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/(.*)",
        "headers": [
          {
            "key": "X-Content-Type-Options",
            "value": "nosniff"
          },
          {
            "key": "X-Frame-Options",
            "value": "DENY"
          },
          {
            "key": "X-XSS-Protection",
            "value": "1; mode=block"
          }
        ]
      }
    ]
  };

  fs.writeFileSync(path.join(vercelDir, 'vercel.json'), JSON.stringify(vercelJsonContent, null, 2));
  
  console.log('✅ Vercel package created');
  return vercelDir;
}

async function createZipArchives() {
  console.log('🗜️  Creating ZIP archives...');
  
  const packages = ['cpanel-public_html', 'netlify', 'vercel'];
  
  for (const pkg of packages) {
    const pkgDir = path.join(DEPLOYMENT_DIR, pkg);
    const zipFile = path.join(DEPLOYMENT_DIR, `${pkg}.zip`);
    
    if (fs.existsSync(pkgDir)) {
      try {
        // Use native zip command if available, otherwise skip
        await execAsync(`cd "${pkgDir}" && zip -r "../${pkg}.zip" .`);
        console.log(`✅ Created ${pkg}.zip`);
      } catch (error) {
        console.log(`⚠️  Could not create ${pkg}.zip (zip command not available)`);
      }
    }
  }
}

function generateDeploymentSummary() {
  const summaryContent = `# Aureus Africa - Deployment Packages

Generated on: ${new Date().toISOString()}

## Available Packages

### 1. cPanel (cpanel-public_html/)
- Ready for upload to cPanel public_html directory
- Includes .htaccess for client-side routing
- Includes deployment instructions

### 2. Netlify (netlify/)
- Ready for Netlify deployment
- Includes _redirects for client-side routing
- Includes netlify.toml configuration

### 3. Vercel (vercel/)
- Ready for Vercel deployment
- Includes vercel.json configuration
- Optimized for Vercel's edge network

## Quick Deployment

### cPanel
1. Upload contents of cpanel-public_html/ to your public_html directory
2. Ensure .htaccess file is uploaded
3. Visit your domain to verify

### Netlify
1. Drag and drop the netlify/ folder to Netlify dashboard
2. Or connect your Git repository and set build command to "npm run build"

### Vercel
1. Import your Git repository to Vercel
2. Vercel will automatically detect the configuration
3. Or upload the vercel/ folder contents

## Environment Variables

Remember to set these environment variables in your hosting platform:
- VITE_SUPABASE_URL
- VITE_SUPABASE_ANON_KEY
- VITE_RESEND_API_KEY
- VITE_RESEND_FROM_EMAIL
- VITE_RESEND_FROM_NAME

## Support

For deployment issues, refer to the documentation or contact support.
`;

  fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'README.md'), summaryContent);
  console.log('✅ Generated deployment summary');
}

async function packageForDeployment() {
  console.log('📦 Aureus Africa - Deployment Packaging');
  console.log('=' .repeat(50));
  
  try {
    await createDeploymentDirectory();
    
    createCPanelPackage();
    createNetlifyPackage();
    createVercelPackage();
    
    await createZipArchives();
    generateDeploymentSummary();
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ Deployment packages created successfully!');
    console.log(`📁 Location: ${DEPLOYMENT_DIR}`);
    console.log('\nAvailable packages:');
    console.log('  • cpanel-public_html/ - For cPanel hosting');
    console.log('  • netlify/ - For Netlify deployment');
    console.log('  • vercel/ - For Vercel deployment');
    console.log('\nRefer to README.md in the deployment-packages directory for instructions.');
    
  } catch (error) {
    console.error('❌ Deployment packaging failed:', error.message);
    process.exit(1);
  }
}

// Run the packaging
if (import.meta.url === `file://${process.argv[1]}`) {
  packageForDeployment();
}

export { packageForDeployment };
