# 🚀 Aureus Africa - Unified Build System

## Overview

This comprehensive build system provides unified development and production workflows for the Aureus Africa trading platform. It ensures both frontend and backend services start correctly and creates deployment-ready builds.

## 🎯 Key Features

- **Unified Development Workflow**: Single command starts both frontend and backend
- **Environment Validation**: Automatic checking of required variables
- **Production-Ready Builds**: Optimized bundles with asset chunking
- **Multi-Platform Deployment**: Packages for cPanel, Netlify, and Vercel
- **Build Integrity Testing**: Comprehensive validation of build output
- **Health Monitoring**: System diagnostics and port checking

## ⚡ Quick Start

```bash
# Initial setup (run once)
npm run setup

# Start development (both services)
npm run dev:full

# Build for production
npm run build

# Create deployment packages
npm run deploy:build
```

## 📋 Complete Script Reference

### Development Scripts
| Command | Description | Ports |
|---------|-------------|-------|
| `npm run dev` | Frontend only (Vite) | 8000 |
| `npm run server` | Backend only (Express) | 8002 |
| `npm run dev:full` | **Both services** (Recommended) | 8000, 8002 |
| `npm run startup` | Guided startup with checks | - |

### Build Scripts
| Command | Description | Output |
|---------|-------------|--------|
| `npm run build` | Full production build | `dist/` |
| `npm run build:clean` | Clean build (removes dist first) | `dist/` |
| `npm run build:analyze` | Build with bundle analysis | `dist/` + stats |
| `npm run preview` | Test production build locally | Port 8000 |

### Deployment Scripts
| Command | Description | Output |
|---------|-------------|--------|
| `npm run deploy:build` | Create all deployment packages | `deployment-packages/` |
| `npm run deploy:package` | Package existing build | `deployment-packages/` |
| `npm run test:build` | Test build integrity | - |

### Validation Scripts
| Command | Description | Purpose |
|---------|-------------|---------|
| `npm run health-check` | System diagnostics | Pre-development |
| `npm run build:env-check` | Environment validation | Pre-build |
| `npm run validate:all` | Code validation | Quality assurance |

## 🔧 Critical Requirements

### **MANDATORY: Both Services Required**

The application **REQUIRES** both services for full functionality:

- **Frontend (8000)**: React app with Vite hot reload
- **Backend (8002)**: Express server with API routes

**⚠️ APIs will NOT work unless both services are running!**

### Recommended Development Flow
```bash
# Terminal 1: Start both services
npm run dev:full

# Terminal 2: Development tasks
npm run validate:all
```

## 🌍 Environment Variables

### Required Client-Side Variables
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_RESEND_API_KEY=re_xxxxxxxxxx
VITE_RESEND_FROM_EMAIL=<EMAIL>
VITE_RESEND_FROM_NAME=Aureus Alliance Holdings
```

### Server-Side Only Variables
```env
SUPABASE_DB_PASSWORD=your-db-password
JWT_SECRET=your-jwt-secret
```

## 🏗️ Build Process

### Production Build Pipeline
1. **Environment Validation** → Checks all VITE_* variables
2. **Clean Build** → Removes previous artifacts
3. **Vite Compilation** → Bundles React app with optimizations
4. **Asset Optimization** → Chunks and compresses resources
5. **Build Verification** → Validates output integrity

### Build Output Structure
```
dist/
├── index.html              # Application entry point
├── manifest.json           # PWA manifest
├── assets/
│   ├── index-[hash].js     # Main application bundle
│   ├── index-[hash].css    # Compiled styles
│   ├── vendor-[hash].js    # Third-party libraries
│   └── supabase-[hash].js  # Supabase client
└── [static assets]         # Images, fonts, etc.
```

## 🚀 Deployment Options

### Automated Deployment Packages
```bash
npm run deploy:build
```

Creates ready-to-deploy packages:

#### **cPanel Package** (`cpanel-public_html/`)
- Upload directly to public_html directory
- Includes .htaccess for client-side routing
- Apache-optimized configuration

#### **Netlify Package** (`netlify/`)
- Drag-and-drop deployment ready
- Includes _redirects for SPA routing
- Netlify.toml configuration

#### **Vercel Package** (`vercel/`)
- Git-based deployment ready
- Vercel.json configuration
- Edge-optimized settings

### Manual Deployment
1. Run `npm run build`
2. Upload `dist/` contents to web server
3. Configure server for client-side routing
4. Set environment variables in hosting platform

## 🔍 Troubleshooting

### Common Issues & Solutions

**"APIs not working"**
```bash
# Check both services are running
npm run health-check

# Start both services
npm run dev:full
```

**"Build fails with environment errors"**
```bash
# Validate environment variables
npm run build:env-check

# Check .env file exists and has required variables
```

**"Blank page after deployment"**
- Check browser console for errors
- Verify all assets load correctly
- Ensure client-side routing is configured

**"404 errors on routes"**
- Configure server to serve index.html for all routes
- Check .htaccess (Apache) or _redirects (Netlify)

### Debug Commands
```bash
npm run startup          # Guided startup with diagnostics
npm run health-check     # System health validation
npm run test:build       # Build integrity testing
npm run build:analyze    # Bundle size analysis
```

## 📊 Performance Optimization

### Bundle Analysis
- Main bundle target: < 500KB
- Automatic vendor chunk splitting
- Asset optimization and compression

### Optimization Commands
```bash
npm run build:analyze    # Analyze bundle sizes
npm run test:build       # Performance validation
```

## 🔒 Security Features

### Environment Security
- Only VITE_* variables included in client bundle
- Server-side secrets remain secure
- Service role keys development-only

### Build Security
- Source maps disabled in production
- Security headers configured
- Asset integrity validation

## 📚 Additional Resources

- [Development Guide](./DEVELOPMENT_GUIDE.md) - Detailed development workflow
- [Vite Documentation](https://vitejs.dev/) - Build tool documentation
- [Supabase Docs](https://supabase.com/docs) - Database and auth
- [React Documentation](https://react.dev/) - Frontend framework

## 🆘 Support

### Getting Help
1. Run `npm run startup` for guided diagnostics
2. Check `npm run health-check` for system status
3. Review error logs in console output
4. Refer to troubleshooting section above

### System Requirements
- Node.js 18+
- npm 8+
- 4GB RAM minimum
- 2GB free disk space

---

**Ready to start?** Run `npm run startup` for guided setup and diagnostics!
