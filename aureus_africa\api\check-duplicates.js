import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  console.warn('⚠️ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY for duplicate check API');
}

const supabase = (supabaseUrl && serviceKey) ? createClient(supabaseUrl, serviceKey) : null;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Server not configured' });
    }

    const { email, username, sponsorUsername } = req.body || {};
    const emailValue = (email || '').trim().toLowerCase();
    const usernameValue = (username || '').trim().toLowerCase();
    const sponsorUsernameValue = (sponsorUsername || '').trim().toLowerCase();

    const results = {
      emailDuplicate: false,
      usernameDuplicate: false,
      sponsorExists: null, // null = not checked, true = exists, false = doesn't exist
    };

    // Check email duplicates - service role bypasses RLS automatically
    if (emailValue) {
      try {
        console.log('🔍 Checking email duplicates for:', emailValue);

        // Check users table with exact match (service role bypasses RLS)
        const { data: usersEmail, error: usersEmailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', emailValue)
          .limit(1);

        if (usersEmailError) {
          console.error('❌ Users email check error:', usersEmailError);
          return res.status(500).json({ error: 'Database error (users.email)' });
        }

        // Check telegram_users table for temp_email
        const { data: telegramEmail, error: telegramEmailError } = await supabase
          .from('telegram_users')
          .select('telegram_id')
          .eq('temp_email', emailValue)
          .limit(1);

        if (telegramEmailError) {
          console.error('❌ Telegram email check error:', telegramEmailError);
          return res.status(500).json({ error: 'Database error (telegram_users.temp_email)' });
        }

        // Email is duplicate if found in any table (only check temp_email for telegram_users)
        results.emailDuplicate = (usersEmail?.length || 0) > 0 ||
                                (telegramEmail?.length || 0) > 0;

        console.log('📧 Email duplicate check result:', {
          emailValue,
          usersFound: usersEmail?.length || 0,
          telegramTempFound: telegramEmail?.length || 0,
          isDuplicate: results.emailDuplicate
        });
      } catch (error) {
        console.error('❌ Email duplicate check error:', error);
        return res.status(500).json({ error: 'Email validation failed' });
      }
    }

    // Check username duplicates - service role bypasses RLS automatically
    if (usernameValue) {
      try {
        console.log('🔍 Checking username duplicates for:', usernameValue);

        // Check users table with case-insensitive match (service role bypasses RLS)
        const { data: usersUsername, error: usersUsernameError } = await supabase
          .from('users')
          .select('id')
          .ilike('username', usernameValue)
          .limit(1);

        if (usersUsernameError) {
          console.error('❌ Users username check error:', usersUsernameError);
          return res.status(500).json({ error: 'Database error (users.username)' });
        }

        // Check telegram_users table
        const { data: telegramUsername, error: telegramUsernameError } = await supabase
          .from('telegram_users')
          .select('telegram_id')
          .ilike('username', usernameValue)
          .limit(1);

        if (telegramUsernameError) {
          console.error('❌ Telegram username check error:', telegramUsernameError);
          return res.status(500).json({ error: 'Database error (telegram_users.username)' });
        }

        // Username is duplicate if found in either table
        results.usernameDuplicate = (usersUsername?.length || 0) > 0 || (telegramUsername?.length || 0) > 0;

        console.log('👤 Username duplicate check result:', {
          usernameValue,
          usersFound: usersUsername?.length || 0,
          telegramFound: telegramUsername?.length || 0,
          isDuplicate: results.usernameDuplicate
        });
      } catch (error) {
        console.error('❌ Username duplicate check error:', error);
        return res.status(500).json({ error: 'Username validation failed' });
      }
    }

    // Check sponsor username exists (for validation that sponsor is valid)
    if (sponsorUsernameValue) {
      const [usersSponsor, telegramSponsor] = await Promise.all([
        supabase.from('users').select('id').ilike('username', sponsorUsernameValue).limit(1),
        supabase.from('telegram_users').select('telegram_id').ilike('username', sponsorUsernameValue).limit(1),
      ]);

      if (usersSponsor.error) {
        console.error('❌ Sponsor check users username error:', usersSponsor.error);
        return res.status(500).json({ error: 'Database error (users.sponsor)' });
      }
      if (telegramSponsor.error) {
        console.error('❌ Sponsor check telegram username error:', telegramSponsor.error);
        return res.status(500).json({ error: 'Database error (telegram_users.sponsor)' });
      }

      // Sponsor exists if found in either table
      results.sponsorExists = (usersSponsor.data?.length || 0) > 0 || (telegramSponsor.data?.length || 0) > 0;
    }

    return res.status(200).json({ success: true, ...results });
  } catch (error) {
    console.error('❌ Duplicate check API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

