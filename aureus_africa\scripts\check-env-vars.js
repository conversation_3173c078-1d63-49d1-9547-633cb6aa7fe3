#!/usr/bin/env node

/**
 * Environment Variables Validation Script
 * Ensures all required environment variables are present before build
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Required environment variables for the application
const REQUIRED_ENV_VARS = {
  // Supabase Configuration (Client-side)
  'VITE_SUPABASE_URL': {
    description: 'Supabase project URL',
    example: 'https://your-project.supabase.co',
    required: true
  },
  'VITE_SUPABASE_ANON_KEY': {
    description: 'Supabase anonymous key for client-side operations',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true
  },
  'VITE_SUPABASE_SERVICE_ROLE_KEY': {
    description: 'Supabase service role key (admin operations)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true,
    warning: 'This should only be used in development. Remove from production builds.'
  },
  
  // Email Service Configuration (Client-side)
  'VITE_RESEND_API_KEY': {
    description: 'Resend API key for email services',
    example: 're_xxxxxxxxxx',
    required: true
  },
  'VITE_RESEND_FROM_EMAIL': {
    description: 'From email address for system emails',
    example: '<EMAIL>',
    required: true
  },
  'VITE_RESEND_FROM_NAME': {
    description: 'From name for system emails',
    example: 'Aureus Alliance Holdings',
    required: true
  }
};

// Optional environment variables
const OPTIONAL_ENV_VARS = {
  'VITE_APP_VERSION': {
    description: 'Application version for display',
    example: '1.0.0'
  },
  'VITE_APP_ENVIRONMENT': {
    description: 'Environment identifier',
    example: 'production'
  }
};

function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found!');
    console.log(`Expected location: ${envPath}`);
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return envVars;
}

function validateEnvironmentVariables() {
  console.log('🔍 Validating environment variables...\n');
  
  const envVars = loadEnvFile();
  const errors = [];
  const warnings = [];
  const missing = [];

  // Check required variables
  Object.entries(REQUIRED_ENV_VARS).forEach(([key, config]) => {
    const value = envVars[key];
    
    if (!value || value.trim() === '') {
      missing.push({
        key,
        description: config.description,
        example: config.example
      });
    } else {
      console.log(`✅ ${key}: ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`);
      
      if (config.warning) {
        warnings.push(`⚠️  ${key}: ${config.warning}`);
      }
    }
  });

  // Check optional variables
  Object.entries(OPTIONAL_ENV_VARS).forEach(([key, config]) => {
    const value = envVars[key];
    
    if (value && value.trim() !== '') {
      console.log(`✅ ${key}: ${value}`);
    } else {
      console.log(`ℹ️  ${key}: Not set (optional)`);
    }
  });

  // Report results
  console.log('\n' + '='.repeat(60));
  
  if (missing.length > 0) {
    console.log('\n❌ MISSING REQUIRED ENVIRONMENT VARIABLES:');
    missing.forEach(({ key, description, example }) => {
      console.log(`\n${key}:`);
      console.log(`  Description: ${description}`);
      console.log(`  Example: ${example}`);
    });
    errors.push(`${missing.length} required environment variables are missing`);
  }

  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:');
    warnings.forEach(warning => console.log(`  ${warning}`));
  }

  if (errors.length === 0) {
    console.log('\n✅ All required environment variables are present!');
    console.log('🚀 Ready to build the application.');
    return true;
  } else {
    console.log('\n❌ Environment validation failed!');
    console.log('\nPlease update your .env file with the missing variables.');
    console.log('Refer to .env.example or the documentation for guidance.');
    return false;
  }
}

function main() {
  console.log('🔧 Aureus Africa - Environment Variables Validation');
  console.log('=' .repeat(60));
  
  const isValid = validateEnvironmentVariables();
  
  if (!isValid) {
    process.exit(1);
  }
  
  console.log('\n✨ Environment validation completed successfully!');
}

// Run the validation
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { validateEnvironmentVariables };
