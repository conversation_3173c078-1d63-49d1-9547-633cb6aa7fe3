import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 8002;
const HOST = process.env.HOST || (process.env.NODE_ENV === 'production' ? '0.0.0.0' : 'localhost');
const NODE_ENV = process.env.NODE_ENV || 'development';

console.log('🚀 Aureus Africa Backend Server');
console.log('=' .repeat(40));
console.log(`Environment: ${NODE_ENV}`);
console.log(`Port: ${PORT}`);
console.log(`Directory: ${__dirname}`);

// Middleware
app.use(cors({
  origin: NODE_ENV === 'production'
    ? ['https://aureusafrica.com', 'https://www.aureusafrica.com']
    : ['http://localhost:8000', 'http://127.0.0.1:8000'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files in production
if (NODE_ENV === 'production') {
  app.use(express.static('dist'));
  console.log('📁 Serving static files from dist/');
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: NODE_ENV,
    version: '1.0.0'
  });
});

// Dynamically load and register API routes
console.log('\n🔌 Loading API routes...');
const apiDir = path.join(__dirname, 'api');

if (fs.existsSync(apiDir)) {
  const loadApiRoutes = async (dir, basePath = '/api') => {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // Recursively load subdirectories
        await loadApiRoutes(itemPath, `${basePath}/${item}`);
      } else if (item.endsWith('.js')) {
        const routeName = item.replace('.js', '');
        const routePath = `${basePath}/${routeName}`;

        try {
          // Convert Windows path to file:// URL for ESM import
          const fileUrl = pathToFileURL(itemPath).href;
          const { default: handler } = await import(fileUrl);

          // Create a wrapper to handle the Next.js-style API route
          app.all(routePath, async (req, res) => {
            try {
              // Create a mock Next.js-style request/response object
              const mockReq = {
                ...req,
                query: { ...req.query, ...req.params },
                body: req.body,
                method: req.method
              };

              const mockRes = {
                status: (code) => {
                  res.status(code);
                  return mockRes;
                },
                json: (data) => {
                  res.json(data);
                  return mockRes;
                },
                end: () => {
                  res.end();
                  return mockRes;
                },
                setHeader: (name, value) => {
                  res.setHeader(name, value);
                  return mockRes;
                },
                send: (data) => {
                  res.send(data);
                  return mockRes;
                }
              };

              await handler(mockReq, mockRes);
            } catch (handlerError) {
              console.error(`❌ API route error ${routePath}:`, handlerError);
              res.status(500).json({
                error: 'Internal server error',
                message: NODE_ENV === 'development' ? handlerError.message : 'Something went wrong'
              });
            }
          });

          console.log(`  ✅ ${routePath}`);
        } catch (error) {
          console.error(`  ❌ Failed to load ${routePath}:`, error.message);
        }
      }
    }
  };

  await loadApiRoutes(apiDir);
  console.log('✅ API routes loaded successfully');
} else {
  console.log('⚠️  API directory not found - no API routes loaded');
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Serve React app for all other routes (SPA fallback)
if (NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    const indexPath = path.join(__dirname, 'dist', 'index.html');
    if (fs.existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      res.status(404).json({ error: 'Application not built. Run npm run build first.' });
    }
  });
}

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({
    error: 'API endpoint not found',
    path: req.path,
    method: req.method
  });
});

// Start server
const server = app.listen(PORT, HOST, () => {
  console.log('\n' + '='.repeat(50));
  console.log('✅ Server started successfully!');

  if (NODE_ENV === 'production') {
    console.log(`🌐 Production URL: http://aureus.africa`);
    console.log(`🏥 Health check: http://aureus.africa/health`);
    console.log(`🔌 API endpoints: http://aureus.africa/api/*`);
    console.log(`📁 Static files served from: ${__dirname}`);
    console.log(`🚀 Running in PRODUCTION mode`);
  } else {
    console.log(`🌐 Server URL: http://localhost:${PORT}`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
    console.log(`🔌 API endpoints: http://localhost:${PORT}/api/*`);
    console.log('\n💡 Development workflow:');
    console.log('   Frontend: npm run dev (port 8000)');
    console.log('   Backend:  npm run server (port 8002)');
    console.log('   Full:     npm run dev:full (both services)');
  }

  console.log('=' .repeat(50));
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
