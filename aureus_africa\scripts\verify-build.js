#!/usr/bin/env node

/**
 * Build Verification Script
 * Validates that the production build is complete and deployment-ready
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');

// Critical files that must exist in the build
const REQUIRED_FILES = [
  'index.html',
  'manifest.json'
];

// Critical directories that should exist
const REQUIRED_DIRECTORIES = [
  'assets'
];

// File patterns to check for
const REQUIRED_PATTERNS = [
  /assets\/index-[a-f0-9]+\.js$/,     // Main JS bundle
  /assets\/index-[a-f0-9]+\.css$/,    // Main CSS bundle
];

function checkFileExists(filePath, description) {
  const fullPath = path.join(DIST_DIR, filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    const stats = fs.statSync(fullPath);
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`✅ ${description}: ${filePath} (${sizeKB} KB)`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} - NOT FOUND`);
    return false;
  }
}

function checkDirectoryExists(dirPath, description) {
  const fullPath = path.join(DIST_DIR, dirPath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
  
  if (exists) {
    const files = fs.readdirSync(fullPath);
    console.log(`✅ ${description}: ${dirPath} (${files.length} files)`);
    return true;
  } else {
    console.log(`❌ ${description}: ${dirPath} - NOT FOUND`);
    return false;
  }
}

function checkFilePattern(pattern, description) {
  const assetsDir = path.join(DIST_DIR, 'assets');
  
  if (!fs.existsSync(assetsDir)) {
    console.log(`❌ ${description}: assets directory not found`);
    return false;
  }

  const files = fs.readdirSync(assetsDir);
  const matchingFiles = files.filter(file => pattern.test(`assets/${file}`));
  
  if (matchingFiles.length > 0) {
    const file = matchingFiles[0];
    const stats = fs.statSync(path.join(assetsDir, file));
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`✅ ${description}: assets/${file} (${sizeKB} KB)`);
    return true;
  } else {
    console.log(`❌ ${description}: No files matching pattern ${pattern}`);
    return false;
  }
}

function validateIndexHtml() {
  const indexPath = path.join(DIST_DIR, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html validation: File not found');
    return false;
  }

  const content = fs.readFileSync(indexPath, 'utf8');
  const checks = [
    {
      test: content.includes('<div id="root">'),
      description: 'React root element'
    },
    {
      test: /assets\/index-[a-f0-9]+\.js/.test(content),
      description: 'Main JavaScript bundle reference'
    },
    {
      test: /assets\/index-[a-f0-9]+\.css/.test(content),
      description: 'Main CSS bundle reference'
    },
    {
      test: content.includes('<!DOCTYPE html>'),
      description: 'Valid HTML5 doctype'
    }
  ];

  let allPassed = true;
  console.log('\n📄 index.html validation:');
  
  checks.forEach(({ test, description }) => {
    if (test) {
      console.log(`  ✅ ${description}`);
    } else {
      console.log(`  ❌ ${description}`);
      allPassed = false;
    }
  });

  return allPassed;
}

function calculateBuildSize() {
  function getDirectorySize(dirPath) {
    let totalSize = 0;
    
    if (!fs.existsSync(dirPath)) return 0;
    
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        totalSize += getDirectorySize(filePath);
      } else {
        totalSize += stats.size;
      }
    });
    
    return totalSize;
  }

  const totalSize = getDirectorySize(DIST_DIR);
  const sizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  
  console.log(`\n📊 Total build size: ${sizeMB} MB`);
  
  if (totalSize > 50 * 1024 * 1024) { // 50MB
    console.log('⚠️  Build size is quite large. Consider optimizing assets.');
  } else {
    console.log('✅ Build size is reasonable.');
  }
  
  return totalSize;
}

function verifyBuild() {
  console.log('🔍 Verifying production build...\n');
  
  // Check if dist directory exists
  if (!fs.existsSync(DIST_DIR)) {
    console.log('❌ dist directory not found! Run "npm run build" first.');
    return false;
  }

  let allChecksPass = true;

  // Check required files
  console.log('📁 Checking required files:');
  REQUIRED_FILES.forEach(file => {
    if (!checkFileExists(file, 'Required file')) {
      allChecksPass = false;
    }
  });

  // Check required directories
  console.log('\n📂 Checking required directories:');
  REQUIRED_DIRECTORIES.forEach(dir => {
    if (!checkDirectoryExists(dir, 'Required directory')) {
      allChecksPass = false;
    }
  });

  // Check file patterns
  console.log('\n🔍 Checking build artifacts:');
  REQUIRED_PATTERNS.forEach((pattern, index) => {
    const descriptions = ['Main JavaScript bundle', 'Main CSS bundle'];
    if (!checkFilePattern(pattern, descriptions[index])) {
      allChecksPass = false;
    }
  });

  // Validate index.html
  if (!validateIndexHtml()) {
    allChecksPass = false;
  }

  // Calculate build size
  calculateBuildSize();

  return allChecksPass;
}

function main() {
  console.log('🚀 Aureus Africa - Build Verification');
  console.log('=' .repeat(50));
  
  const isValid = verifyBuild();
  
  console.log('\n' + '='.repeat(50));
  
  if (isValid) {
    console.log('✅ Build verification completed successfully!');
    console.log('🎉 Your application is ready for deployment.');
    console.log('\nNext steps:');
    console.log('  • Test locally: npm run preview');
    console.log('  • Deploy to production: Upload dist/ folder to your web server');
  } else {
    console.log('❌ Build verification failed!');
    console.log('Please fix the issues above and rebuild the application.');
    process.exit(1);
  }
}

// Run the verification
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { verifyBuild };
