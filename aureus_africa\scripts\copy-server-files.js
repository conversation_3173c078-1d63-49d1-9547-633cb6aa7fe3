/**
 * Copy server files to dist folder for complete production deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const distDir = path.join(rootDir, 'dist');

console.log('📦 Copying server files for production deployment...');

// Ensure dist directory exists
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Function to copy files recursively
function copyRecursive(src, dest, excludePatterns = []) {
  const stats = fs.statSync(src);
  
  if (stats.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    files.forEach(file => {
      const srcPath = path.join(src, file);
      const destPath = path.join(dest, file);
      
      // Check if file should be excluded
      const shouldExclude = excludePatterns.some(pattern => {
        if (typeof pattern === 'string') {
          return file === pattern;
        } else if (pattern instanceof RegExp) {
          return pattern.test(file);
        }
        return false;
      });
      
      if (!shouldExclude) {
        copyRecursive(srcPath, destPath, excludePatterns);
      }
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

// Files and directories to copy
const serverFiles = [
  {
    src: 'server.js',
    dest: 'server.js',
    description: 'Main server file'
  },
  {
    src: 'api',
    dest: 'api',
    description: 'API endpoints',
    exclude: ['.DS_Store', 'Thumbs.db']
  },
  {
    src: 'package.json',
    dest: 'package.json',
    description: 'Package configuration'
  }
];

// Copy each file/directory
serverFiles.forEach(({ src, dest, description, exclude = [] }) => {
  const srcPath = path.join(rootDir, src);
  const destPath = path.join(distDir, dest);
  
  if (fs.existsSync(srcPath)) {
    console.log(`📁 Copying ${description}: ${src} -> dist/${dest}`);
    copyRecursive(srcPath, destPath, exclude);
  } else {
    console.warn(`⚠️ Source not found: ${src}`);
  }
});

// Create production package.json with only production dependencies
console.log('📝 Creating production package.json...');
const originalPackage = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));

const productionPackage = {
  name: originalPackage.name,
  version: originalPackage.version,
  type: originalPackage.type,
  scripts: {
    start: "node server.js",
    "start:production": "NODE_ENV=production node server.js"
  },
  dependencies: {
    // Only include production dependencies needed for the server
    "@supabase/supabase-js": originalPackage.dependencies["@supabase/supabase-js"],
    "bcryptjs": originalPackage.dependencies["bcryptjs"],
    "cors": originalPackage.dependencies["cors"],
    "dotenv": originalPackage.dependencies["dotenv"],
    "express": originalPackage.dependencies["express"],
    "multer": originalPackage.dependencies["multer"],
    "resend": originalPackage.dependencies["resend"]
  }
};

fs.writeFileSync(
  path.join(distDir, 'package.json'), 
  JSON.stringify(productionPackage, null, 2)
);

// Create deployment instructions
const deploymentInstructions = `# Aureus Africa - Production Deployment

## Quick Start
1. Upload the entire 'dist' folder to your server
2. Navigate to the dist folder on your server
3. Install dependencies: \`npm install --production\`
4. Create .env file with your production environment variables
5. Start the server: \`npm start\`

## Environment Variables Required
Create a .env file in the dist folder with:

\`\`\`
# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Email Configuration  
RESEND_API_KEY=your_resend_api_key
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings

# JWT Secret
JWT_SECRET=your_jwt_secret_key

# Production Settings
NODE_ENV=production
PORT=8002
\`\`\`

## Server Commands
- Start server: \`npm start\`
- Start with production settings: \`npm run start:production\`

## Files Included
- Frontend: Static files (index.html, assets/)
- Backend: API server (server.js, api/)
- Dependencies: Production package.json

## Notes
- The frontend files are served from the root
- API endpoints are available at /api/*
- Server runs on port 8002 by default (configurable via PORT env var)
`;

fs.writeFileSync(path.join(distDir, 'DEPLOYMENT.md'), deploymentInstructions);

console.log('✅ Production build complete!');
console.log('');
console.log('📋 Build Summary:');
console.log('================');
console.log('📁 Frontend: Static files ready for deployment');
console.log('🚀 Backend: API server included');
console.log('📦 Dependencies: Production-only package.json created');
console.log('📖 Instructions: DEPLOYMENT.md created');
console.log('');
console.log('🎯 Next Steps:');
console.log('1. Upload the entire "dist" folder to your server');
console.log('2. Run "npm install --production" in the dist folder');
console.log('3. Create .env file with production variables');
console.log('4. Run "npm start" to start the server');
console.log('');
console.log('🌐 Your complete application is ready for deployment!');
