#!/bin/bash

# Production Build Script for Aureus Alliance Website
# Run this on your local machine before uploading to server

set -e

echo "🏗️ Building Aureus Alliance Website for Production..."

# Navigate to project directory
cd "$(dirname "$0")/.."

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create production environment file if it doesn't exist
if [ ! -f .env.production ]; then
    echo "⚠️  Creating .env.production file..."
    cat > .env.production << 'EOF'
# Production Environment Variables
NODE_ENV=production
PORT=8002

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Application Configuration
VITE_APP_NAME=Aureus Alliance Holdings
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production

# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings

# Debug Mode
VITE_DEBUG=false
EOF
    echo "⚠️  Please edit .env.production with your actual values!"
    exit 1
fi

# Build the application
echo "🏗️ Building React application..."
npm run build

# Create deployment package
echo "📦 Creating deployment package..."
mkdir -p deployment/package

# Copy necessary files
cp -r dist deployment/package/
cp server.js deployment/package/
cp package.json deployment/package/
cp package-lock.json deployment/package/
cp -r api deployment/package/
cp .env.production deployment/package/.env

# Create PM2 ecosystem file
cat > deployment/package/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'aureus-alliance',
    script: 'server.js',
    instances: 1,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 8002
    },
    error_file: '/var/log/aureus-alliance/error.log',
    out_file: '/var/log/aureus-alliance/out.log',
    log_file: '/var/log/aureus-alliance/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# Create archive
cd deployment/package
tar -czf ../aureus-alliance-production.tar.gz .
cd ../..

echo "✅ Production build complete!"
echo "📦 Deployment package: deployment/aureus-alliance-production.tar.gz"
echo ""
echo "Upload this file to your server and extract it to /var/www/aureus-alliance"
